import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'
import { db } from '../database/connection'
import { userSettings } from '../database/schema'
import { eq } from 'drizzle-orm'
import { v4 as uuidv4 } from 'uuid'
import crypto from 'crypto'

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-encryption-key-32-chars-long'

export class SettingsController {
  /**
   * 加密API Key
   */
  private encryptApiKey(apiKey: string): string {
    const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY)
    let encrypted = cipher.update(apiKey, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    return encrypted
  }

  /**
   * 解密API Key
   */
  private decryptApiKey(encryptedApiKey: string): string {
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY)
      let decrypted = decipher.update(encryptedApiKey, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      return decrypted
    } catch (error) {
      logger.warn('解密API Key失败:', error)
      return ''
    }
  }

  /**
   * 获取用户设置
   */
  async getUserSettings(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id

      const settings = await db()
        .select()
        .from(userSettings)
        .where(eq(userSettings.userId, userId))
        .limit(1)

      let userSettingsData = settings[0]

      // 如果用户设置不存在，创建默认设置
      if (!userSettingsData) {
        const defaultSettings = {
          id: uuidv4(),
          userId,
          aiApiKeys: null,
          preferredCurrency: 'USD',
          timezone: 'UTC',
          dashboardLayout: JSON.stringify({
            widgets: [
              { id: 'assets', position: { x: 0, y: 0, w: 6, h: 4 } },
              { id: 'market', position: { x: 6, y: 0, w: 6, h: 4 } },
              { id: 'news', position: { x: 0, y: 4, w: 12, h: 4 } },
            ],
          }),
          updatedAt: new Date(),
        }

        const newSettings = await db()
          .insert(userSettings)
          .values(defaultSettings)
          .returning()

        userSettingsData = newSettings[0]
      }

      // 解密API Keys用于前端显示（只显示是否已设置）
      let apiKeysStatus = {}
      if (userSettingsData?.aiApiKeys) {
        try {
          const encryptedKeys = JSON.parse(userSettingsData.aiApiKeys)
          apiKeysStatus = Object.keys(encryptedKeys).reduce((acc, provider) => {
            acc[provider] = { configured: true, masked: '••••••••' }
            return acc
          }, {} as Record<string, any>)
        } catch (error) {
          logger.warn('解析API Keys失败:', error)
        }
      }

      const responseData = {
        id: userSettingsData!.id,
        userId: userSettingsData!.userId,
        aiApiKeys: apiKeysStatus,
        preferredCurrency: userSettingsData!.preferredCurrency,
        timezone: userSettingsData!.timezone,
        dashboardLayout: userSettingsData!.dashboardLayout
          ? JSON.parse(userSettingsData!.dashboardLayout)
          : null,
        updatedAt: userSettingsData!.updatedAt,
      }

      res.json({
        success: true,
        data: responseData,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户设置失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户设置失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 更新用户设置
   */
  async updateUserSettings(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const updateData = req.body

      // 处理API Keys加密
      if (updateData.aiApiKeys) {
        const encryptedKeys: Record<string, string> = {}
        for (const [provider, apiKey] of Object.entries(updateData.aiApiKeys)) {
          if (apiKey && typeof apiKey === 'string' && apiKey !== '••••••••') {
            encryptedKeys[provider] = this.encryptApiKey(apiKey)
          }
        }
        updateData.aiApiKeys = JSON.stringify(encryptedKeys)
      }

      // 处理仪表板布局
      if (
        updateData.dashboardLayout &&
        typeof updateData.dashboardLayout === 'object'
      ) {
        updateData.dashboardLayout = JSON.stringify(updateData.dashboardLayout)
      }

      // 更新设置
      const updatedSettings = await db()
        .update(userSettings)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(userSettings.userId, userId))
        .returning()

      if (!updatedSettings[0]) {
        res.status(404).json({
          success: false,
          message: '用户设置不存在',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 返回更新后的设置（不包含敏感信息）
      let apiKeysStatus = {}
      if (updatedSettings[0].aiApiKeys) {
        try {
          const encryptedKeys = JSON.parse(updatedSettings[0].aiApiKeys)
          apiKeysStatus = Object.keys(encryptedKeys).reduce((acc, provider) => {
            acc[provider] = { configured: true, masked: '••••••••' }
            return acc
          }, {} as Record<string, any>)
        } catch (error) {
          logger.warn('解析API Keys失败:', error)
        }
      }

      const responseData = {
        id: updatedSettings[0].id,
        userId: updatedSettings[0].userId,
        aiApiKeys: apiKeysStatus,
        preferredCurrency: updatedSettings[0].preferredCurrency,
        timezone: updatedSettings[0].timezone,
        dashboardLayout: updatedSettings[0].dashboardLayout
          ? JSON.parse(updatedSettings[0].dashboardLayout)
          : null,
        updatedAt: updatedSettings[0].updatedAt,
      }

      res.json({
        success: true,
        data: responseData,
        message: '设置更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('更新用户设置失败:', error)
      res.status(500).json({
        success: false,
        message: '更新用户设置失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 重置用户设置
   */
  async resetUserSettings(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id

      const defaultSettings = {
        aiApiKeys: null,
        preferredCurrency: 'USD',
        timezone: 'UTC',
        dashboardLayout: JSON.stringify({
          widgets: [
            { id: 'assets', position: { x: 0, y: 0, w: 6, h: 4 } },
            { id: 'market', position: { x: 6, y: 0, w: 6, h: 4 } },
            { id: 'news', position: { x: 0, y: 4, w: 12, h: 4 } },
          ],
        }),
        updatedAt: new Date(),
      }

      const resetSettings = await db()
        .update(userSettings)
        .set(defaultSettings)
        .where(eq(userSettings.userId, userId))
        .returning()

      if (!resetSettings[0]) {
        res.status(404).json({
          success: false,
          message: '用户设置不存在',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const responseData = {
        id: resetSettings[0].id,
        userId: resetSettings[0].userId,
        aiApiKeys: {},
        preferredCurrency: resetSettings[0].preferredCurrency,
        timezone: resetSettings[0].timezone,
        dashboardLayout: resetSettings[0].dashboardLayout
          ? JSON.parse(resetSettings[0].dashboardLayout)
          : null,
        updatedAt: resetSettings[0].updatedAt,
      }

      res.json({
        success: true,
        data: responseData,
        message: '设置重置成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('重置用户设置失败:', error)
      res.status(500).json({
        success: false,
        message: '重置用户设置失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取支持的货币列表
   */
  async getSupportedCurrencies(req: Request, res: Response): Promise<void> {
    try {
      const currencies = [
        { code: 'USD', name: '美元', symbol: '$' },
        { code: 'CNY', name: '人民币', symbol: '¥' },
        { code: 'EUR', name: '欧元', symbol: '€' },
        { code: 'GBP', name: '英镑', symbol: '£' },
        { code: 'JPY', name: '日元', symbol: '¥' },
        { code: 'KRW', name: '韩元', symbol: '₩' },
        { code: 'HKD', name: '港币', symbol: 'HK$' },
        { code: 'SGD', name: '新加坡元', symbol: 'S$' },
      ]

      res.json({
        success: true,
        data: currencies,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取支持的货币列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取支持的货币列表失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取支持的时区列表
   */
  async getSupportedTimezones(req: Request, res: Response): Promise<void> {
    try {
      const timezones = [
        { value: 'UTC', label: 'UTC (协调世界时)', offset: '+00:00' },
        { value: 'Asia/Shanghai', label: '北京时间 (CST)', offset: '+08:00' },
        { value: 'Asia/Tokyo', label: '东京时间 (JST)', offset: '+09:00' },
        { value: 'Asia/Seoul', label: '首尔时间 (KST)', offset: '+09:00' },
        { value: 'Asia/Hong_Kong', label: '香港时间 (HKT)', offset: '+08:00' },
        { value: 'Asia/Singapore', label: '新加坡时间 (SGT)', offset: '+08:00' },
        {
          value: 'Europe/London',
          label: '伦敦时间 (GMT/BST)',
          offset: '+00:00/+01:00',
        },
        {
          value: 'Europe/Paris',
          label: '巴黎时间 (CET/CEST)',
          offset: '+01:00/+02:00',
        },
        {
          value: 'America/New_York',
          label: '纽约时间 (EST/EDT)',
          offset: '-05:00/-04:00',
        },
        {
          value: 'America/Los_Angeles',
          label: '洛杉矶时间 (PST/PDT)',
          offset: '-08:00/-07:00',
        },
      ]

      res.json({
        success: true,
        data: timezones,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取支持的时区列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取支持的时区列表失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const settingsController = new SettingsController()
