{"name": "ai-asset-manager-backend", "version": "1.0.0", "description": "AI驱动的资产管理应用后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/index.js", "start:prod": "NODE_ENV=production node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push"}, "keywords": ["ai", "asset-management", "finance", "investment", "typescript", "express"], "author": "AI Asset Manager Team", "license": "MIT", "dependencies": {"@ai-sdk/anthropic": "^0.0.21", "@ai-sdk/openai": "^0.0.24", "@langchain/anthropic": "^0.3.24", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66", "@langchain/langgraph": "^0.3.11", "@langchain/openai": "^0.6.3", "ai": "^2.2.26", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "better-sqlite3": "^9.2.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "drizzle-orm": "^0.29.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.30", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.8", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "drizzle-kit": "^0.20.7", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=22.0.0", "pnpm": ">=8.0.0"}}