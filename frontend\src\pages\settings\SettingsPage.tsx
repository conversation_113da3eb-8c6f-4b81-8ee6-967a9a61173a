import React, { useState, useEffect } from 'react'
import {
  Typography,
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Divider,
  message,
  Tabs,
  Row,
  Col,
  Alert,
  Modal,
  Upload,
} from 'antd'
import {
  SaveOutlined,
  ReloadOutlined,
  KeyOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  ExportOutlined,
  ImportOutlined,
  UploadOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons'
import { settingsService, type UserSettings } from '../../services/settingsService'

const { Title, Paragraph } = Typography
const { Option } = Select
const { TabPane } = Tabs
const { Password } = Input

export const SettingsPage: React.FC = () => {
  const [form] = Form.useForm()
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [testingConnection, setTestingConnection] = useState(false)
  const [exportModalVisible, setExportModalVisible] = useState(false)
  const [importModalVisible, setImportModalVisible] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const response = await settingsService.getUserSettings()
      if (response.success && response.data) {
        setSettings(response.data)
        form.setFieldsValue({
          aiApiKeys: response.data.aiApiKeys || {},
          preferredCurrency: response.data.preferredCurrency || 'CNY',
          timezone: response.data.timezone || 'Asia/Shanghai',
        })
      }
    } catch (error) {
      message.error('加载设置失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (values: any) => {
    try {
      setSaving(true)
      const response = await settingsService.updateUserSettings(values)
      if (response.success && response.data) {
        setSettings(response.data)
        message.success('设置保存成功')
      }
    } catch (error) {
      message.error('保存设置失败')
    } finally {
      setSaving(false)
    }
  }

  const handleTestConnection = async (provider: string) => {
    try {
      setTestingConnection(true)
      const apiKey = form.getFieldValue(['aiApiKeys', provider])
      if (!apiKey) {
        message.warning('请先输入API密钥')
        return
      }

      const response = await settingsService.validateApiKey(provider, apiKey)
      if (response.success && response.data?.valid) {
        message.success(`${provider.toUpperCase()} API连接测试成功`)
      } else {
        message.error(
          `${provider.toUpperCase()} API连接测试失败: ${
            response.data?.message || '未知错误'
          }`
        )
      }
    } catch (error) {
      message.error('连接测试失败')
    } finally {
      setTestingConnection(false)
    }
  }

  const handleExportSettings = async () => {
    try {
      await settingsService.exportSettings()
      message.success('设置导出成功')
      setExportModalVisible(false)
    } catch (error) {
      message.error('导出失败')
    }
  }

  const handleImportSettings = async (file: File) => {
    try {
      const response = await settingsService.importSettings(file)
      if (response.success) {
        message.success('设置导入成功')
        setImportModalVisible(false)
        await loadSettings()
      } else {
        message.error(response.message || '导入失败')
      }
    } catch (error) {
      message.error('导入失败')
    }
  }

  const handleReset = () => {
    Modal.confirm({
      title: '重置设置',
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await settingsService.resetUserSettings()
          if (response.success) {
            message.success('设置已重置')
            await loadSettings()
          }
        } catch (error) {
          message.error('重置失败')
        }
      },
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Title level={2} className="mb-0">
          系统设置
        </Title>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadSettings}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />} onClick={() => setExportModalVisible(true)}>
            导出设置
          </Button>
          <Button icon={<ImportOutlined />} onClick={() => setImportModalVisible(true)}>
            导入设置
          </Button>
        </Space>
      </div>

      <Card loading={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={{
            preferredCurrency: 'CNY',
            timezone: 'Asia/Shanghai',
          }}
        >
          <Tabs defaultActiveKey="general">
            {/* 通用设置 */}
            <TabPane
              tab={
                <span>
                  <SettingOutlined />
                  通用设置
                </span>
              }
              key="general"
            >
              <Row gutter={[24, 0]}>
                <Col xs={24} lg={12}>
                  <Card title="基础设置" size="small">
                    <Form.Item
                      name="preferredCurrency"
                      label="默认货币"
                      tooltip="选择资产显示的默认货币单位"
                    >
                      <Select>
                        <Option value="CNY">人民币 (¥)</Option>
                        <Option value="USD">美元 ($)</Option>
                        <Option value="EUR">欧元 (€)</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item name="timezone" label="时区" tooltip="选择您所在的时区">
                      <Select>
                        <Option value="Asia/Shanghai">北京时间 (UTC+8)</Option>
                        <Option value="UTC">协调世界时 (UTC)</Option>
                        <Option value="America/New_York">纽约时间 (UTC-5)</Option>
                        <Option value="Europe/London">伦敦时间 (UTC+0)</Option>
                      </Select>
                    </Form.Item>
                  </Card>
                </Col>

                <Col xs={24} lg={12}>
                  <Card title="显示设置" size="small">
                    <Alert
                      message="主题和语言设置"
                      description="主题和语言设置功能正在开发中，敬请期待。"
                      type="info"
                      showIcon
                    />
                  </Card>
                </Col>
              </Row>
            </TabPane>

            {/* API设置 */}
            <TabPane
              tab={
                <span>
                  <KeyOutlined />
                  API设置
                </span>
              }
              key="api"
            >
              <Alert
                message="API密钥安全提示"
                description="请妥善保管您的API密钥，不要与他人分享。所有密钥都会加密存储。"
                type="warning"
                showIcon
                className="mb-6"
              />

              <Row gutter={[24, 24]}>
                <Col xs={24} lg={8}>
                  <Card title="OpenAI API" size="small">
                    <Form.Item
                      name={['aiApiKeys', 'openai']}
                      label="API Key"
                      tooltip="用于GPT模型的API密钥"
                    >
                      <Password
                        placeholder="sk-..."
                        iconRender={(visible) =>
                          visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                        }
                      />
                    </Form.Item>
                    <Button
                      size="small"
                      onClick={() => handleTestConnection('openai')}
                      loading={testingConnection}
                    >
                      测试连接
                    </Button>
                  </Card>
                </Col>

                <Col xs={24} lg={8}>
                  <Card title="Anthropic API" size="small">
                    <Form.Item
                      name={['aiApiKeys', 'anthropic']}
                      label="API Key"
                      tooltip="用于Claude模型的API密钥"
                    >
                      <Password
                        placeholder="sk-ant-..."
                        iconRender={(visible) =>
                          visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                        }
                      />
                    </Form.Item>
                    <Button
                      size="small"
                      onClick={() => handleTestConnection('anthropic')}
                      loading={testingConnection}
                    >
                      测试连接
                    </Button>
                  </Card>
                </Col>

                <Col xs={24} lg={8}>
                  <Card title="DeepSeek API" size="small">
                    <Form.Item
                      name={['aiApiKeys', 'deepseek']}
                      label="API Key"
                      tooltip="用于DeepSeek模型的API密钥"
                    >
                      <Password
                        placeholder="sk-..."
                        iconRender={(visible) =>
                          visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                        }
                      />
                    </Form.Item>
                    <Button
                      size="small"
                      onClick={() => handleTestConnection('deepseek')}
                      loading={testingConnection}
                    >
                      测试连接
                    </Button>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            {/* 安全设置 */}
            <TabPane
              tab={
                <span>
                  <SecurityScanOutlined />
                  安全设置
                </span>
              }
              key="security"
            >
              <Row gutter={[24, 0]}>
                <Col xs={24}>
                  <Card title="安全选项" size="small">
                    <Alert
                      message="安全功能开发中"
                      description="密码修改、会话管理等安全功能正在开发中，敬请期待。"
                      type="info"
                      showIcon
                    />
                  </Card>
                </Col>
              </Row>
            </TabPane>

            {/* 数据管理 */}
            <TabPane
              tab={
                <span>
                  <ExportOutlined />
                  数据管理
                </span>
              }
              key="data"
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} lg={12}>
                  <Card title="数据备份" size="small">
                    <Paragraph>
                      定期备份您的数据以防止意外丢失。备份包括资产信息、AI对话记录和个人设置。
                    </Paragraph>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button
                        type="primary"
                        icon={<ExportOutlined />}
                        block
                        onClick={() => setExportModalVisible(true)}
                      >
                        导出设置
                      </Button>
                      <Button
                        icon={<ImportOutlined />}
                        block
                        onClick={() => setImportModalVisible(true)}
                      >
                        导入设置
                      </Button>
                    </Space>
                  </Card>
                </Col>

                <Col xs={24} lg={12}>
                  <Card title="数据清理" size="small">
                    <Paragraph>
                      清理不需要的数据以释放存储空间。请谨慎操作，删除的数据无法恢复。
                    </Paragraph>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Button disabled>清理AI对话记录</Button>
                      <Button disabled>清理历史数据</Button>
                      <Button danger onClick={handleReset}>
                        重置所有设置
                      </Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </TabPane>
          </Tabs>

          <Divider />

          <div className="flex justify-between">
            <Button danger onClick={handleReset}>
              重置设置
            </Button>
            <Space>
              <Button onClick={() => form.resetFields()}>取消</Button>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={saving}
              >
                保存设置
              </Button>
            </Space>
          </div>
        </Form>
      </Card>

      {/* 导出设置模态框 */}
      <Modal
        title="导出设置"
        open={exportModalVisible}
        onOk={handleExportSettings}
        onCancel={() => setExportModalVisible(false)}
        okText="导出"
        cancelText="取消"
      >
        <p>
          确定要导出当前设置吗？导出的文件将包含您的偏好设置，但不包含敏感信息如API密钥。
        </p>
      </Modal>

      {/* 导入设置模态框 */}
      <Modal
        title="导入设置"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          accept=".json"
          beforeUpload={(file) => {
            handleImportSettings(file)
            return false
          }}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单个JSON格式的设置文件上传</p>
        </Upload.Dragger>
      </Modal>
    </div>
  )
}
