import { db } from '../database/connection'
import { assets, assetCategories, assetHistory } from '../database/schema'
import { eq, and, desc } from 'drizzle-orm'
import { logger } from '../utils/logger'
import type { Asset, AssetCategory, AssetHistory } from '../types'
import { v4 as uuidv4 } from 'uuid'

export class AssetService {
  /**
   * 获取用户所有资产
   */
  async getUserAssets(userId: string): Promise<Asset[]> {
    try {
      const userAssets = await db()
        .select()
        .from(assets)
        .where(eq(assets.userId, userId))
        .orderBy(desc(assets.updatedAt))

      logger.info(`获取用户资产成功: ${userId}, 数量: ${userAssets.length}`)
      return userAssets
    } catch (error) {
      logger.error('获取用户资产失败:', error)
      throw error
    }
  }

  /**
   * 根据分类获取资产
   */
  async getAssetsByCategory(userId: string, categoryId: string): Promise<Asset[]> {
    try {
      const categoryAssets = await db()
        .select()
        .from(assets)
        .where(and(eq(assets.userId, userId), eq(assets.categoryId, categoryId)))
        .orderBy(desc(assets.updatedAt))

      return categoryAssets
    } catch (error) {
      logger.error('根据分类获取资产失败:', error)
      throw error
    }
  }

  /**
   * 获取单个资产详情
   */
  async getAssetById(userId: string, assetId: string): Promise<Asset | null> {
    try {
      const asset = await db()
        .select()
        .from(assets)
        .where(and(eq(assets.id, assetId), eq(assets.userId, userId)))
        .limit(1)

      return asset[0] || null
    } catch (error) {
      logger.error('获取资产详情失败:', error)
      throw error
    }
  }

  /**
   * 创建新资产
   */
  async createAsset(
    userId: string,
    assetData: Omit<Asset, 'id' | 'userId' | 'createdAt' | 'updatedAt'>
  ): Promise<Asset> {
    try {
      const newAsset = await db()
        .insert(assets)
        .values({
          id: uuidv4(),
          userId,
          ...assetData,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning()

      if (!newAsset[0]) {
        throw new Error('资产创建失败')
      }

      // 创建初始历史记录
      await this.addAssetHistory(newAsset[0].id, newAsset[0].currentValue)

      logger.info(`资产创建成功: ${newAsset[0].id}`)
      return newAsset[0]
    } catch (error) {
      logger.error('创建资产失败:', error)
      throw error
    }
  }

  /**
   * 更新资产
   */
  async updateAsset(
    userId: string,
    assetId: string,
    updateData: Partial<Asset>
  ): Promise<Asset> {
    try {
      // 检查资产是否属于用户
      const existingAsset = await this.getAssetById(userId, assetId)
      if (!existingAsset) {
        throw new Error('资产不存在或无权限')
      }

      const updatedAsset = await db()
        .update(assets)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(and(eq(assets.id, assetId), eq(assets.userId, userId)))
        .returning()

      if (!updatedAsset[0]) {
        throw new Error('资产更新失败')
      }

      // 如果更新了价值，添加历史记录
      if (
        updateData.currentValue &&
        updateData.currentValue !== existingAsset.currentValue
      ) {
        await this.addAssetHistory(assetId, updateData.currentValue)
      }

      logger.info(`资产更新成功: ${assetId}`)
      return updatedAsset[0]
    } catch (error) {
      logger.error('更新资产失败:', error)
      throw error
    }
  }

  /**
   * 删除资产
   */
  async deleteAsset(userId: string, assetId: string): Promise<void> {
    try {
      // 检查资产是否属于用户
      const existingAsset = await this.getAssetById(userId, assetId)
      if (!existingAsset) {
        throw new Error('资产不存在或无权限')
      }

      await db()
        .delete(assets)
        .where(and(eq(assets.id, assetId), eq(assets.userId, userId)))

      logger.info(`资产删除成功: ${assetId}`)
    } catch (error) {
      logger.error('删除资产失败:', error)
      throw error
    }
  }

  /**
   * 获取所有资产分类
   */
  async getAssetCategories(): Promise<AssetCategory[]> {
    try {
      const categories = await db().select().from(assetCategories)

      return categories
    } catch (error) {
      logger.error('获取资产分类失败:', error)
      throw error
    }
  }

  /**
   * 获取资产历史记录
   */
  async getAssetHistory(
    userId: string,
    assetId: string,
    limit: number = 30
  ): Promise<AssetHistory[]> {
    try {
      // 检查资产是否属于用户
      const asset = await this.getAssetById(userId, assetId)
      if (!asset) {
        throw new Error('资产不存在或无权限')
      }

      const history = await db()
        .select()
        .from(assetHistory)
        .where(eq(assetHistory.assetId, assetId))
        .orderBy(desc(assetHistory.recordDate))
        .limit(limit)

      return history
    } catch (error) {
      logger.error('获取资产历史失败:', error)
      throw error
    }
  }

  /**
   * 添加资产历史记录
   */
  async addAssetHistory(
    assetId: string,
    value: number,
    recordDate?: string
  ): Promise<AssetHistory> {
    try {
      const historyId: string = uuidv4()
      const now = new Date()
      const finalRecordDate = recordDate ? recordDate : now.toISOString().split('T')[0]

      const historyRecord = await db()
        .insert(assetHistory)
        .values({
          id: historyId,
          assetId: assetId,
          value: value,
          recordDate: finalRecordDate as string,
          createdAt: now,
        })
        .returning()

      if (!historyRecord[0]) {
        throw new Error('历史记录创建失败')
      }

      return historyRecord[0]
    } catch (error) {
      logger.error('添加资产历史失败:', error)
      throw error
    }
  }

  /**
   * 获取用户资产统计
   */
  async getUserAssetStats(userId: string): Promise<{
    totalValue: number
    totalAssets: number
    categoryBreakdown: Array<{
      categoryId: string
      categoryName: string
      totalValue: number
      assetCount: number
      percentage: number
    }>
  }> {
    try {
      const userAssets = await this.getUserAssets(userId)
      const categories = await this.getAssetCategories()

      const totalValue = userAssets.reduce((sum, asset) => sum + asset.currentValue, 0)
      const totalAssets = userAssets.length

      // 按分类统计
      const categoryMap = new Map<
        string,
        { name: string; totalValue: number; assetCount: number }
      >()

      categories.forEach((category) => {
        categoryMap.set(category.id, {
          name: category.name,
          totalValue: 0,
          assetCount: 0,
        })
      })

      userAssets.forEach((asset) => {
        const categoryData = categoryMap.get(asset.categoryId)
        if (categoryData) {
          categoryData.totalValue += asset.currentValue
          categoryData.assetCount += 1
        }
      })

      const categoryBreakdown = Array.from(categoryMap.entries()).map(
        ([categoryId, data]) => ({
          categoryId,
          categoryName: data.name,
          totalValue: data.totalValue,
          assetCount: data.assetCount,
          percentage: totalValue > 0 ? (data.totalValue / totalValue) * 100 : 0,
        })
      )

      return {
        totalValue,
        totalAssets,
        categoryBreakdown,
      }
    } catch (error) {
      logger.error('获取用户资产统计失败:', error)
      throw error
    }
  }

  /**
   * 批量更新资产价值
   */
  async batchUpdateAssetValues(
    userId: string,
    updates: Array<{ assetId: string; newValue: number }>
  ): Promise<void> {
    try {
      for (const update of updates) {
        await this.updateAsset(userId, update.assetId, {
          currentValue: update.newValue,
        })
      }

      logger.info(`批量更新资产价值成功: ${userId}, 更新数量: ${updates.length}`)
    } catch (error) {
      logger.error('批量更新资产价值失败:', error)
      throw error
    }
  }

  /**
   * 搜索资产
   */
  async searchAssets(userId: string, query: string): Promise<Asset[]> {
    try {
      const userAssets = await this.getUserAssets(userId)

      // 简单的文本搜索
      const filteredAssets = userAssets.filter(
        (asset) =>
          asset.name.toLowerCase().includes(query.toLowerCase()) ||
          (asset.description &&
            asset.description.toLowerCase().includes(query.toLowerCase()))
      )

      return filteredAssets
    } catch (error) {
      logger.error('搜索资产失败:', error)
      throw error
    }
  }
}

export const assetService = new AssetService()
