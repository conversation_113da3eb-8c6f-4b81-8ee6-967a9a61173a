import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { Layout } from './components/layout/Layout'
import { Dashboard } from './pages/dashboard/Dashboard'
import { useUserStore } from './stores/userStore'

// 懒加载页面组件
const AssetsPage = React.lazy(() =>
  import('./pages/assets/AssetsPage').then((module) => ({ default: module.AssetsPage }))
)
const LiabilitiesPage = React.lazy(() =>
  import('./pages/liabilities/LiabilitiesPage').then((module) => ({
    default: module.LiabilitiesPage,
  }))
)
const AIInsightsPage = React.lazy(() =>
  import('./pages/ai-insights/AIInsightsPage').then((module) => ({
    default: module.AIInsightsPage,
  }))
)
const NewsPage = React.lazy(() =>
  import('./pages/news/NewsPage').then((module) => ({ default: module.NewsPage }))
)
const SettingsPage = React.lazy(() =>
  import('./pages/settings/SettingsPage').then((module) => ({
    default: module.SettingsPage,
  }))
)

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useUserStore()

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// 登录页面占位符
const LoginPage: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="max-w-md w-full space-y-8">
      <div className="text-center">
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          登录到 AI 资产管理系统
        </h2>
        <p className="mt-2 text-sm text-gray-600">登录页面将在后续开发中完善</p>
      </div>
    </div>
  </div>
)

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <div className="App">
          <Routes>
            {/* <Route path="/login" element={<LoginPage />} /> */}
            <Route
              path="/"
              element={
                // <ProtectedRoute>
                <Layout />
                // </ProtectedRoute>
              }
            >
              <Route index element={<Dashboard />} />
              <Route
                path="assets"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <AssetsPage />
                  </React.Suspense>
                }
              />
              <Route
                path="liabilities"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <LiabilitiesPage />
                  </React.Suspense>
                }
              />
              <Route
                path="ai-insights"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <AIInsightsPage />
                  </React.Suspense>
                }
              />
              <Route
                path="news"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <NewsPage />
                  </React.Suspense>
                }
              />
              <Route
                path="settings"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <SettingsPage />
                  </React.Suspense>
                }
              />
            </Route>
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  )
}

export default App
