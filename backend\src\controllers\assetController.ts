import { Request, Response } from 'express'
import { assetService } from '../services/assetService'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'

export class AssetController {
  /**
   * 获取用户所有资产
   */
  async getUserAssets(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const assets = await assetService.getUserAssets(userId)

      res.json({
        success: true,
        data: assets,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户资产失败:', error)
      res.status(500).json({
        success: false,
        message: '获取资产失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 根据分类获取资产
   */
  async getAssetsByCategory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { categoryId } = req.params

      if (!categoryId) {
        res.status(400).json({
          success: false,
          message: '分类ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const assets = await assetService.getAssetsByCategory(userId, categoryId)

      res.json({
        success: true,
        data: assets,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('根据分类获取资产失败:', error)
      res.status(500).json({
        success: false,
        message: '获取资产失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取单个资产详情
   */
  async getAssetById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { assetId } = req.params

      if (!assetId) {
        res.status(400).json({
          success: false,
          message: '资产ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const asset = await assetService.getAssetById(userId, assetId)

      if (!asset) {
        res.status(404).json({
          success: false,
          message: '资产不存在',
          timestamp: new Date().toISOString(),
        })
        return
      }

      res.json({
        success: true,
        data: asset,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取资产详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取资产详情失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 创建新资产
   */
  async createAsset(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const assetData = req.body

      // 基本验证
      if (
        !assetData.name ||
        !assetData.categoryId ||
        assetData.currentValue === undefined
      ) {
        res.status(400).json({
          success: false,
          message: '资产名称、分类和当前价值为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 验证价值为正数
      if (assetData.currentValue < 0) {
        res.status(400).json({
          success: false,
          message: '资产价值不能为负数',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const newAsset = await assetService.createAsset(userId, assetData)

      res.status(201).json({
        success: true,
        data: newAsset,
        message: '资产创建成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('创建资产失败:', error)
      res.status(500).json({
        success: false,
        message: '创建资产失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 更新资产
   */
  async updateAsset(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { assetId } = req.params
      const updateData = req.body

      if (!assetId) {
        res.status(400).json({
          success: false,
          message: '资产ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 移除不允许更新的字段
      delete updateData.id
      delete updateData.userId
      delete updateData.createdAt
      delete updateData.updatedAt

      // 验证价值为正数
      if (updateData.currentValue !== undefined && updateData.currentValue < 0) {
        res.status(400).json({
          success: false,
          message: '资产价值不能为负数',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const updatedAsset = await assetService.updateAsset(userId, assetId, updateData)

      res.json({
        success: true,
        data: updatedAsset,
        message: '资产更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('更新资产失败:', error)

      if (error.message === '资产不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '更新资产失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 删除资产
   */
  async deleteAsset(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { assetId } = req.params

      if (!assetId) {
        res.status(400).json({
          success: false,
          message: '资产ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      await assetService.deleteAsset(userId, assetId)

      res.json({
        success: true,
        message: '资产删除成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('删除资产失败:', error)

      if (error.message === '资产不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '删除资产失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 获取所有资产分类
   */
  async getAssetCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = await assetService.getAssetCategories()

      res.json({
        success: true,
        data: categories,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取资产分类失败:', error)
      res.status(500).json({
        success: false,
        message: '获取资产分类失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取资产历史记录
   */
  async getAssetHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { assetId } = req.params
      const { limit } = req.query

      if (!assetId) {
        res.status(400).json({
          success: false,
          message: '资产ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const limitNum = limit ? parseInt(limit as string, 10) : 30
      const history = await assetService.getAssetHistory(userId, assetId, limitNum)

      res.json({
        success: true,
        data: history,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取资产历史失败:', error)

      if (error.message === '资产不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '获取资产历史失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 添加资产历史记录
   */
  async addAssetHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { assetId } = req.params
      const { value, recordDate } = req.body

      if (!assetId) {
        res.status(400).json({
          success: false,
          message: '资产ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      if (value === undefined) {
        res.status(400).json({
          success: false,
          message: '价值为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 验证资产权限
      const asset = await assetService.getAssetById(userId, assetId)
      if (!asset) {
        res.status(404).json({
          success: false,
          message: '资产不存在或无权限',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const historyRecord = await assetService.addAssetHistory(
        assetId,
        value,
        recordDate
      )

      res.status(201).json({
        success: true,
        data: historyRecord,
        message: '历史记录添加成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('添加资产历史失败:', error)
      res.status(500).json({
        success: false,
        message: '添加历史记录失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取用户资产统计
   */
  async getUserAssetStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const stats = await assetService.getUserAssetStats(userId)

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户资产统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取资产统计失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 批量更新资产价值
   */
  async batchUpdateAssetValues(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    try {
      const userId = req.user.id
      const { updates } = req.body

      if (!Array.isArray(updates) || updates.length === 0) {
        res.status(400).json({
          success: false,
          message: '更新数据格式不正确',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 验证更新数据格式
      for (const update of updates) {
        if (!update.assetId || update.newValue === undefined) {
          res.status(400).json({
            success: false,
            message: '每个更新项必须包含assetId和newValue',
            timestamp: new Date().toISOString(),
          })
          return
        }

        if (update.newValue < 0) {
          res.status(400).json({
            success: false,
            message: '资产价值不能为负数',
            timestamp: new Date().toISOString(),
          })
          return
        }
      }

      await assetService.batchUpdateAssetValues(userId, updates)

      res.json({
        success: true,
        message: '批量更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('批量更新资产价值失败:', error)
      res.status(500).json({
        success: false,
        message: '批量更新失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 搜索资产
   */
  async searchAssets(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { q } = req.query

      if (!q || typeof q !== 'string') {
        res.status(400).json({
          success: false,
          message: '搜索关键词为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const assets = await assetService.searchAssets(userId, q)

      res.json({
        success: true,
        data: assets,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('搜索资产失败:', error)
      res.status(500).json({
        success: false,
        message: '搜索资产失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const assetController = new AssetController()
