# AI 驱动资产管理应用 - 项目文件结构

## 项目根目录结构

```
ai-asset-manager/
├── README.md                          # 项目说明文档
├── LICENSE                            # 开源许可证
├── .gitignore                         # Git忽略文件
├── .env.example                       # 环境变量模板
├── .env                              # 环境变量文件（不提交到Git）
├── package.json                       # 项目依赖和脚本
├── pnpm-workspace.yaml               # pnpm工作空间配置
├── tsconfig.json                     # TypeScript根配置
├── docker-compose.yml                # Docker编排文件
├── docker-compose.dev.yml            # 开发环境Docker配置
├── ecosystem.config.js               # PM2进程管理配置
├── nginx.conf                        # Nginx配置文件
├── scripts/                          # 部署和维护脚本
│   ├── deploy.sh                     # 一键部署脚本
│   ├── backup.sh                     # 数据备份脚本
│   ├── health-check.sh               # 健康检查脚本
│   └── update.sh                     # 应用更新脚本
├── docs/                             # 项目文档
│   ├── api/                          # API文档
│   ├── deployment/                   # 部署文档
│   ├── development/                  # 开发文档
│   └── user-guide/                   # 用户指南
├── frontend/                         # 前端应用
├── backend/                          # 后端应用
├── shared/                           # 共享代码和类型定义
├── data/                             # 数据文件（SQLite数据库等）
├── logs/                             # 日志文件
└── tests/                            # 端到端测试
```

## 前端项目结构 (frontend/)

```
frontend/
├── package.json                      # 前端依赖配置
├── tsconfig.json                     # TypeScript配置
├── vite.config.ts                    # Vite构建配置
├── tailwind.config.js                # Tailwind CSS配置
├── index.html                        # HTML入口文件
├── public/                           # 静态资源
│   ├── favicon.ico
│   ├── logo.png
│   └── manifest.json
├── src/                              # 源代码
│   ├── main.tsx                      # 应用入口
│   ├── App.tsx                       # 根组件
│   ├── vite-env.d.ts                 # Vite类型定义
│   ├── components/                   # 可复用组件
│   │   ├── ui/                       # 基础UI组件
│   │   │   ├── Button/
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Button.module.css
│   │   │   │   └── index.ts
│   │   │   ├── Input/
│   │   │   ├── Modal/
│   │   │   ├── Table/
│   │   │   ├── Chart/
│   │   │   └── index.ts              # 统一导出
│   │   ├── layout/                   # 布局组件
│   │   │   ├── Header/
│   │   │   │   ├── Header.tsx
│   │   │   │   ├── Header.module.css
│   │   │   │   └── index.ts
│   │   │   ├── Sidebar/
│   │   │   ├── Footer/
│   │   │   └── Layout/
│   │   ├── charts/                   # 图表组件
│   │   │   ├── AssetChart/
│   │   │   ├── DebtChart/
│   │   │   ├── MarketChart/
│   │   │   └── VirtualizedChart/
│   │   ├── forms/                    # 表单组件
│   │   │   ├── AssetForm/
│   │   │   ├── DebtForm/
│   │   │   └── SettingsForm/
│   │   ├── ai/                       # AI相关组件
│   │   │   ├── ChatInterface/
│   │   │   ├── ModelSelector/
│   │   │   ├── StreamingResponse/
│   │   │   └── ConversationHistory/
│   │   └── common/                   # 通用组件
│   │       ├── Loading/
│   │       ├── ErrorBoundary/
│   │       ├── Pagination/
│   │       └── DatePicker/
│   ├── pages/                        # 页面组件
│   │   ├── Dashboard/
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Dashboard.module.css
│   │   │   ├── components/           # 页面专用组件
│   │   │   │   ├── AssetOverview/
│   │   │   │   ├── DebtSummary/
│   │   │   │   └── MarketWidget/
│   │   │   └── index.ts
│   │   ├── Assets/
│   │   │   ├── AssetList/
│   │   │   ├── AssetDetail/
│   │   │   ├── AssetCreate/
│   │   │   └── AssetEdit/
│   │   ├── Debts/
│   │   │   ├── DebtList/
│   │   │   ├── DebtDetail/
│   │   │   ├── DebtCreate/
│   │   │   └── PaymentHistory/
│   │   ├── AI/
│   │   │   ├── ChatPage/
│   │   │   ├── ConversationList/
│   │   │   └── ModelSettings/
│   │   ├── News/
│   │   │   ├── NewsList/
│   │   │   ├── NewsDetail/
│   │   │   └── NewsSettings/
│   │   ├── Calendar/
│   │   │   ├── CalendarView/
│   │   │   ├── EventCreate/
│   │   │   └── EventEdit/
│   │   ├── Settings/
│   │   │   ├── GeneralSettings/
│   │   │   ├── APIKeySettings/
│   │   │   ├── NotificationSettings/
│   │   │   └── SecuritySettings/
│   │   ├── Auth/
│   │   │   ├── Login/
│   │   │   ├── Register/
│   │   │   └── ForgotPassword/
│   │   └── NotFound/
│   ├── hooks/                        # 自定义Hooks
│   │   ├── useAuth.ts                # 认证相关
│   │   ├── useApi.ts                 # API调用
│   │   ├── useWebSocket.ts           # WebSocket连接
│   │   ├── useLocalStorage.ts        # 本地存储
│   │   ├── useDebounce.ts            # 防抖处理
│   │   ├── useInfiniteScroll.ts      # 无限滚动
│   │   ├── useChart.ts               # 图表数据处理
│   │   └── useRealtimeData.ts        # 实时数据订阅
│   ├── stores/                       # 状态管理
│   │   ├── authStore.ts              # 认证状态
│   │   ├── assetStore.ts             # 资产状态
│   │   ├── debtStore.ts              # 负债状态
│   │   ├── aiStore.ts                # AI对话状态
│   │   ├── newsStore.ts              # 新闻状态
│   │   ├── marketStore.ts            # 市场数据状态
│   │   ├── settingsStore.ts          # 用户设置状态
│   │   └── index.ts                  # 状态管理入口
│   ├── services/                     # 服务层
│   │   ├── api/                      # API服务
│   │   │   ├── client.ts             # HTTP客户端配置
│   │   │   ├── auth.ts               # 认证API
│   │   │   ├── assets.ts             # 资产API
│   │   │   ├── debts.ts              # 负债API
│   │   │   ├── ai.ts                 # AI API
│   │   │   ├── news.ts               # 新闻API
│   │   │   ├── market.ts             # 市场数据API
│   │   │   └── calendar.ts           # 日历API
│   │   ├── websocket.ts              # WebSocket服务
│   │   ├── storage.ts                # 本地存储服务
│   │   ├── notification.ts           # 通知服务
│   │   └── analytics.ts              # 分析服务
│   ├── utils/                        # 工具函数
│   │   ├── format.ts                 # 格式化工具
│   │   ├── validation.ts             # 验证工具
│   │   ├── date.ts                   # 日期处理
│   │   ├── currency.ts               # 货币处理
│   │   ├── chart.ts                  # 图表工具
│   │   ├── export.ts                 # 数据导出
│   │   └── constants.ts              # 常量定义
│   ├── types/                        # 类型定义
│   │   ├── api.ts                    # API类型
│   │   ├── auth.ts                   # 认证类型
│   │   ├── asset.ts                  # 资产类型
│   │   ├── debt.ts                   # 负债类型
│   │   ├── ai.ts                     # AI类型
│   │   ├── news.ts                   # 新闻类型
│   │   ├── market.ts                 # 市场数据类型
│   │   ├── calendar.ts               # 日历类型
│   │   └── common.ts                 # 通用类型
│   ├── styles/                       # 样式文件
│   │   ├── globals.css               # 全局样式
│   │   ├── variables.css             # CSS变量
│   │   ├── components.css            # 组件样式
│   │   └── utilities.css             # 工具类样式
│   └── assets/                       # 静态资源
│       ├── images/
│       ├── icons/
│       └── fonts/
├── .eslintrc.js                      # ESLint配置
├── .prettierrc                       # Prettier配置
├── .gitignore                        # Git忽略文件
└── Dockerfile                        # Docker构建文件
```

## 后端项目结构 (backend/)

```
backend/
├── package.json                      # 后端依赖配置
├── tsconfig.json                     # TypeScript配置
├── nodemon.json                      # 开发环境配置
├── jest.config.js                    # 测试配置
├── src/                              # 源代码
│   ├── index.ts                      # 应用入口
│   ├── app.ts                        # Express应用配置
│   ├── config/                       # 配置文件
│   │   ├── database.ts               # 数据库配置
│   │   ├── auth.ts                   # 认证配置
│   │   ├── ai.ts                     # AI服务配置
│   │   ├── external-apis.ts          # 外部API配置
│   │   └── index.ts                  # 配置入口
│   ├── controllers/                  # 控制器层
│   │   ├── auth.controller.ts        # 认证控制器
│   │   ├── asset.controller.ts       # 资产控制器
│   │   ├── debt.controller.ts        # 负债控制器
│   │   ├── ai.controller.ts          # AI控制器
│   │   ├── news.controller.ts        # 新闻控制器
│   │   ├── market.controller.ts      # 市场数据控制器
│   │   ├── calendar.controller.ts    # 日历控制器
│   │   ├── settings.controller.ts    # 设置控制器
│   │   └── health.controller.ts      # 健康检查控制器
│   ├── services/                     # 服务层
│   │   ├── auth.service.ts           # 认证服务
│   │   ├── asset.service.ts          # 资产服务
│   │   ├── debt.service.ts           # 负债服务
│   │   ├── ai.service.ts             # AI服务
│   │   ├── news.service.ts           # 新闻服务
│   │   ├── market.service.ts         # 市场数据服务
│   │   ├── calendar.service.ts       # 日历服务
│   │   ├── notification.service.ts   # 通知服务
│   │   ├── encryption.service.ts     # 加密服务
│   │   └── websocket.service.ts      # WebSocket服务
│   ├── repositories/                 # 数据访问层
│   │   ├── base.repository.ts        # 基础仓库
│   │   ├── user.repository.ts        # 用户仓库
│   │   ├── asset.repository.ts       # 资产仓库
│   │   ├── debt.repository.ts        # 负债仓库
│   │   ├── news.repository.ts        # 新闻仓库
│   │   ├── market.repository.ts      # 市场数据仓库
│   │   └── calendar.repository.ts    # 日历仓库
│   ├── database/                     # 数据库相关
│   │   ├── connection.ts             # 数据库连接
│   │   ├── schema.ts                 # 数据库模式定义
│   │   ├── migrations/               # 数据库迁移
│   │   │   ├── 001_initial.sql
│   │   │   ├── 002_add_ai_tables.sql
│   │   │   └── 003_add_indexes.sql
│   │   └── seeds/                    # 种子数据
│   │       ├── categories.ts
│   │       ├── ai_models.ts
│   │       └── market_indicators.ts
│   ├── ai/                           # AI相关模块
│   │   ├── models/                   # AI模型封装
│   │   │   ├── base-model.ts         # 基础模型接口
│   │   │   ├── openai-model.ts       # OpenAI模型
│   │   │   ├── anthropic-model.ts    # Anthropic模型
│   │   │   └── deepseek-model.ts     # DeepSeek模型
│   │   ├── agents/                   # AI Agent
│   │   │   ├── base-agent.ts         # 基础Agent
│   │   │   ├── investment-advisor.ts # 投资建议Agent
│   │   │   ├── asset-analyzer.ts     # 资产分析Agent
│   │   │   ├── news-analyzer.ts      # 新闻分析Agent
│   │   │   └── financial-planner.ts  # 财务规划Agent
│   │   ├── tools/                    # AI工具函数
│   │   │   ├── data-query.ts         # 数据查询工具
│   │   │   ├── calculation.ts        # 计算工具
│   │   │   ├── chart-generator.ts    # 图表生成工具
│   │   │   └── news-retrieval.ts     # 新闻检索工具
│   │   ├── workflows/                # Langgraph工作流
│   │   │   ├── investment-advice.ts  # 投资建议工作流
│   │   │   ├── portfolio-analysis.ts # 组合分析工作流
│   │   │   └── risk-assessment.ts    # 风险评估工作流
│   │   └── model-manager.ts          # 模型管理器
│   ├── external/                     # 外部服务集成
│   │   ├── news-apis/                # 新闻API集成
│   │   │   ├── base-news-api.ts
│   │   │   ├── mcp-news-server.ts
│   │   │   └── rss-feed.ts
│   │   ├── market-apis/              # 市场数据API
│   │   │   ├── alpha-vantage.ts
│   │   │   ├── yahoo-finance.ts
│   │   │   └── polygon.ts
│   │   └── notification/             # 通知服务
│   │       ├── email.ts
│   │       ├── webhook.ts
│   │       └── push.ts
│   ├── middleware/                   # 中间件
│   │   ├── auth.middleware.ts        # 认证中间件
│   │   ├── validation.middleware.ts  # 验证中间件
│   │   ├── rate-limit.middleware.ts  # 限流中间件
│   │   ├── cors.middleware.ts        # CORS中间件
│   │   ├── logging.middleware.ts     # 日志中间件
│   │   ├── error.middleware.ts       # 错误处理中间件
│   │   └── monitoring.middleware.ts  # 监控中间件
│   ├── routes/                       # 路由定义
│   │   ├── index.ts                  # 路由入口
│   │   ├── auth.routes.ts            # 认证路由
│   │   ├── asset.routes.ts           # 资产路由
│   │   ├── debt.routes.ts            # 负债路由
│   │   ├── ai.routes.ts              # AI路由
│   │   ├── news.routes.ts            # 新闻路由
│   │   ├── market.routes.ts          # 市场数据路由
│   │   ├── calendar.routes.ts        # 日历路由
│   │   ├── settings.routes.ts        # 设置路由
│   │   └── health.routes.ts          # 健康检查路由
│   ├── jobs/                         # 定时任务
│   │   ├── data-updater.ts           # 数据更新任务
│   │   ├── news-fetcher.ts           # 新闻抓取任务
│   │   ├── market-updater.ts         # 市场数据更新
│   │   ├── backup.ts                 # 数据备份任务
│   │   └── cleanup.ts                # 数据清理任务
│   ├── utils/                        # 工具函数
│   │   ├── logger.ts                 # 日志工具
│   │   ├── validator.ts              # 验证工具
│   │   ├── crypto.ts                 # 加密工具
│   │   ├── date.ts                   # 日期工具
│   │   ├── format.ts                 # 格式化工具
│   │   ├── error.ts                  # 错误处理工具
│   │   └── constants.ts              # 常量定义
│   ├── types/                        # 类型定义
│   │   ├── express.d.ts              # Express类型扩展
│   │   ├── auth.ts                   # 认证类型
│   │   ├── database.ts               # 数据库类型
│   │   ├── ai.ts                     # AI类型
│   │   ├── external.ts               # 外部服务类型
│   │   └── common.ts                 # 通用类型
│   └── tests/                        # 单元测试
│       ├── controllers/
│       ├── services/
│       ├── repositories/
│       ├── utils/
│       └── fixtures/                 # 测试数据
├── dist/                             # 编译输出目录
├── .eslintrc.js                      # ESLint配置
├── .prettierrc                       # Prettier配置
├── .gitignore                        # Git忽略文件
└── Dockerfile                        # Docker构建文件
```

## 共享代码结构 (shared/)

```
shared/
├── package.json                      # 共享包配置
├── tsconfig.json                     # TypeScript配置
├── src/
│   ├── types/                        # 共享类型定义
│   │   ├── api.ts                    # API接口类型
│   │   ├── auth.ts                   # 认证类型
│   │   ├── asset.ts                  # 资产类型
│   │   ├── debt.ts                   # 负债类型
│   │   ├── ai.ts                     # AI类型
│   │   ├── news.ts                   # 新闻类型
│   │   ├── market.ts                 # 市场数据类型
│   │   ├── calendar.ts               # 日历类型
│   │   └── common.ts                 # 通用类型
│   ├── constants/                    # 共享常量
│   │   ├── api.ts                    # API常量
│   │   ├── errors.ts                 # 错误代码
│   │   ├── currencies.ts             # 货币常量
│   │   └── categories.ts             # 分类常量
│   ├── utils/                        # 共享工具函数
│   │   ├── validation.ts             # 验证工具
│   │   ├── format.ts                 # 格式化工具
│   │   ├── date.ts                   # 日期工具
│   │   └── currency.ts               # 货币工具
│   └── schemas/                      # 验证模式
│       ├── auth.schema.ts            # 认证验证
│       ├── asset.schema.ts           # 资产验证
│       ├── debt.schema.ts            # 负债验证
│       └── settings.schema.ts        # 设置验证
├── dist/                             # 编译输出
└── index.ts                          # 导出入口
```

## 测试结构 (tests/)

```
tests/
├── e2e/                              # 端到端测试
│   ├── auth.spec.ts                  # 认证流程测试
│   ├── asset-management.spec.ts      # 资产管理测试
│   ├── debt-management.spec.ts       # 负债管理测试
│   ├── ai-chat.spec.ts               # AI对话测试
│   └── dashboard.spec.ts             # 仪表板测试
├── integration/                      # 集成测试
│   ├── api/                          # API集成测试
│   ├── database/                     # 数据库集成测试
│   └── external-services/            # 外部服务集成测试
├── fixtures/                         # 测试数据
│   ├── users.json
│   ├── assets.json
│   ├── debts.json
│   └── market-data.json
├── utils/                            # 测试工具
│   ├── setup.ts                      # 测试环境设置
│   ├── teardown.ts                   # 测试清理
│   ├── mock-data.ts                  # 模拟数据
│   └── test-helpers.ts               # 测试辅助函数
├── playwright.config.ts              # Playwright配置
└── jest.config.js                    # Jest配置
```

## 配置文件详细说明

### 根目录配置文件

#### package.json

```json
{
  "name": "ai-asset-manager",
  "version": "1.0.0",
  "description": "AI驱动的资产管理和理财投资建议应用",
  "private": true,
  "workspaces": ["frontend", "backend", "shared"],
  "scripts": {
    "dev": "concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"",
    "dev:frontend": "pnpm --filter frontend dev",
    "dev:backend": "pnpm --filter backend dev",
    "build": "pnpm run build:shared && pnpm run build:backend && pnpm run build:frontend",
    "build:frontend": "pnpm --filter frontend build",
    "build:backend": "pnpm --filter backend build",
    "build:shared": "pnpm --filter shared build",
    "test": "pnpm run test:unit && pnpm run test:integration && pnpm run test:e2e",
    "test:unit": "pnpm --filter backend test && pnpm --filter frontend test",
    "test:integration": "jest --config tests/jest.config.js",
    "test:e2e": "playwright test",
    "lint": "pnpm --filter frontend lint && pnpm --filter backend lint",
    "lint:fix": "pnpm --filter frontend lint:fix && pnpm --filter backend lint:fix",
    "db:migrate": "pnpm --filter backend db:migrate",
    "db:seed": "pnpm --filter backend db:seed",
    "docker:build": "docker-compose build",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down",
    "deploy": "./scripts/deploy.sh",
    "backup": "./scripts/backup.sh"
  },
  "devDependencies": {
    "@playwright/test": "^1.40.0",
    "concurrently": "^8.2.0",
    "jest": "^29.7.0",
    "typescript": "^5.2.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "pnpm": ">=8.0.0"
  }
}
```

#### pnpm-workspace.yaml

```yaml
packages:
  - 'frontend'
  - 'backend'
  - 'shared'
```

#### tsconfig.json

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022"],
    "module": "ESNext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "allowJs": true,
    "outDir": "./dist",
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@shared/*": ["./shared/src/*"],
      "@frontend/*": ["./frontend/src/*"],
      "@backend/*": ["./backend/src/*"]
    }
  },
  "references": [
    { "path": "./frontend" },
    { "path": "./backend" },
    { "path": "./shared" }
  ]
}
```

#### .env.example

```env
# 应用配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# 数据库配置
DATABASE_PATH=./data/database.sqlite

# 认证配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
ENCRYPTION_KEY=your-encryption-key

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# 外部API配置
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
NEWS_API_KEY=your-news-api-key

# 通知配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 监控配置
LOG_LEVEL=info
ENABLE_MONITORING=true

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 开发工作流建议

### 1. 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd ai-asset-manager

# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 初始化数据库
pnpm run db:migrate
pnpm run db:seed

# 启动开发服务器
pnpm run dev
```

### 2. 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 代码规范
- 使用 Conventional Commits 提交规范
- 每个功能模块都要有对应的测试

### 3. Git 工作流

```bash
# 功能开发分支
git checkout -b feature/asset-management
git add .
git commit -m "feat: add asset management functionality"
git push origin feature/asset-management

# 创建 Pull Request 进行代码审查
```

### 4. 部署流程

```bash
# 构建项目
pnpm run build

# 运行测试
pnpm run test

# 部署到生产环境
pnpm run deploy
```

这个项目结构设计考虑了：

- **模块化**: 清晰的功能模块划分
- **可维护性**: 良好的代码组织和文档
- **可扩展性**: 插件化架构支持功能扩展
- **开发效率**: 完善的开发工具链和自动化流程
- **代码质量**: 严格的类型检查和测试覆盖
- **部署便利**: 多种部
