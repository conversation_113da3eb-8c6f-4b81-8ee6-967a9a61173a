import React, { useState, useEffect, useRef } from 'react'
import {
  Typography,
  Card,
  Input,
  Button,
  Select,
  Space,
  List,
  Avatar,
  Spin,
  message,
  Divider,
  Tag,
  Modal,
  Row,
  Col,
  Tabs,
  Progress,
  Timeline,
  Alert,
  Collapse,
} from 'antd'
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  PlusOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  <PERSON><PERSON>hartOutlined,
  DollarOutlined,
  SafetyOutlined,
  GlobalOutlined,
  TeamOutlined,
} from '@ant-design/icons'
import { aiService } from '../../services/aiService'
import { assetService } from '../../services/assetService'
import { marketService } from '../../services/marketService'
import type {
  AIConversation,
  AIMessage,
  AIModel,
  WorkflowExecution,
  WorkflowState,
  AgentResult,
} from '../../services/aiService'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input
const { Option } = Select
const { TabPane } = Tabs
const { Panel } = Collapse

export const AIInsightsPage: React.FC = () => {
  // 原有聊天功能状态
  const [conversations, setConversations] = useState<AIConversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<AIConversation | null>(
    null
  )
  const [messages, setMessages] = useState<AIMessage[]>([])
  const [models, setModels] = useState<AIModel[]>([])
  const [selectedModel, setSelectedModel] = useState<string>('deepseek-chat')
  const [inputMessage, setInputMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [streamingMessage, setStreamingMessage] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [newConversationModal, setNewConversationModal] = useState(false)
  const [conversationTitle, setConversationTitle] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 工作流功能状态
  const [workflowExecution, setWorkflowExecution] = useState<WorkflowExecution | null>(
    null
  )
  const [workflowLoading, setWorkflowLoading] = useState(false)
  const [workflowResults, setWorkflowResults] = useState<AgentResult[]>([])
  const [activeTab, setActiveTab] = useState('comprehensive')
  const [assets, setAssets] = useState<any[]>([])

  useEffect(() => {
    loadConversations()
    loadModels()
    loadAssets()
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const loadAssets = async () => {
    try {
      const response = await assetService.getAssets()
      if (response.success && response.data) {
        setAssets(response.data)
      }
    } catch (error) {
      console.error('加载资产失败:', error)
    }
  }

  const loadConversations = async () => {
    try {
      const response = await aiService.getUserConversations()
      if (response.success && response.data) {
        setConversations(response.data)
        if (response.data.length > 0 && !currentConversation) {
          setCurrentConversation(response.data[0])
          loadMessages(response.data[0].id)
        }
      }
    } catch (error) {
      message.error('加载对话列表失败')
    }
  }

  const loadModels = async () => {
    try {
      const response = await aiService.getAvailableModels()
      if (response.success && response.data) {
        setModels(response.data)
      }
    } catch (error) {
      message.error('加载AI模型失败')
    }
  }

  const loadMessages = async (conversationId: string) => {
    try {
      const response = await aiService.getConversationMessages(conversationId)
      if (response.success && response.data) {
        setMessages(response.data)
      }
    } catch (error) {
      message.error('加载消息失败')
    }
  }

  const createNewConversation = async () => {
    if (!conversationTitle.trim()) {
      message.error('请输入对话标题')
      return
    }

    try {
      const response = await aiService.createConversation({
        title: conversationTitle,
        contextType: 'general',
      })

      if (response.success && response.data) {
        setConversations([response.data, ...conversations])
        setCurrentConversation(response.data)
        setMessages([])
        setNewConversationModal(false)
        setConversationTitle('')
        message.success('新对话创建成功')
      }
    } catch (error) {
      message.error('创建对话失败')
    }
  }

  const deleteConversation = async (conversationId: string) => {
    try {
      const response = await aiService.deleteConversation(conversationId)
      if (response.success) {
        const updatedConversations = conversations.filter(
          (c) => c.id !== conversationId
        )
        setConversations(updatedConversations)

        if (currentConversation?.id === conversationId) {
          if (updatedConversations.length > 0) {
            setCurrentConversation(updatedConversations[0])
            loadMessages(updatedConversations[0].id)
          } else {
            setCurrentConversation(null)
            setMessages([])
          }
        }
        message.success('对话删除成功')
      }
    } catch (error) {
      message.error('删除对话失败')
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || !currentConversation) {
      return
    }

    const userMessage = inputMessage
    setInputMessage('')
    setLoading(true)
    setIsStreaming(true)
    setStreamingMessage('')

    // 添加用户消息到界面
    const tempUserMessage: AIMessage = {
      id: Date.now().toString(),
      conversationId: currentConversation.id,
      modelId: selectedModel,
      role: 'user',
      content: userMessage,
      createdAt: new Date(),
    }
    setMessages((prev) => [...prev, tempUserMessage])

    try {
      // 使用流式发送
      await aiService.sendMessageStream(
        currentConversation.id,
        {
          content: userMessage,
          modelId: selectedModel,
        },
        // onChunk
        (chunk: string) => {
          setStreamingMessage((prev) => prev + chunk)
        },
        // onComplete
        (aiMessage: AIMessage) => {
          setMessages((prev) => [...prev.slice(0, -1), tempUserMessage, aiMessage])
          setStreamingMessage('')
          setIsStreaming(false)
          setLoading(false)
        },
        // onError
        (error: string) => {
          message.error('发送消息失败: ' + error)
          setStreamingMessage('')
          setIsStreaming(false)
          setLoading(false)
        }
      )
    } catch (error) {
      message.error('发送消息失败')
      setStreamingMessage('')
      setIsStreaming(false)
      setLoading(false)
    }
  }

  // 工作流相关函数
  const executeWorkflow = async () => {
    if (assets.length === 0) {
      message.error('请先添加一些资产')
      return
    }

    setWorkflowLoading(true)
    setWorkflowResults([])
    setWorkflowExecution(null)

    try {
      const response = await aiService.executeWorkflow({
        assets,
        modelId: selectedModel,
      })

      if (response.success && response.data) {
        setWorkflowExecution(response.data)
        setWorkflowResults(response.data.results || [])
        message.success('工作流执行完成')
      } else {
        message.error('工作流执行失败')
      }
    } catch (error) {
      message.error('工作流执行时发生错误')
      console.error('工作流执行失败:', error)
    } finally {
      setWorkflowLoading(false)
    }
  }

  const executeSpecificAgent = async (agentType: string) => {
    if (assets.length === 0) {
      message.error('请先添加一些资产')
      return
    }

    setWorkflowLoading(true)

    try {
      const response = await aiService.executeAgent({
        agentType,
        assets,
        modelId: selectedModel,
      })

      if (response.success && response.data) {
        // 更新特定代理的结果
        setWorkflowResults((prev) => {
          const filtered = prev.filter((r) => r.agentType !== agentType)
          return [...filtered, response.data!]
        })
        message.success(`${getAgentName(agentType)}执行完成`)
      } else {
        message.error(`${getAgentName(agentType)}执行失败`)
      }
    } catch (error) {
      message.error(`${getAgentName(agentType)}执行时发生错误`)
      console.error(`${agentType}执行失败:`, error)
    } finally {
      setWorkflowLoading(false)
    }
  }

  const getAgentName = (agentType: string): string => {
    const names: Record<string, string> = {
      financial_analysis: '财务分析代理',
      investment_advisor: '投资建议代理',
      risk_assessment: '风险评估代理',
      news_analysis: '新闻分析代理',
      coordinator: '协调代理',
    }
    return names[agentType] || agentType
  }

  const getAgentIcon = (agentType: string) => {
    const icons: Record<string, React.ReactNode> = {
      financial_analysis: <BarChartOutlined />,
      investment_advisor: <DollarOutlined />,
      risk_assessment: <SafetyOutlined />,
      news_analysis: <GlobalOutlined />,
      coordinator: <TeamOutlined />,
    }
    return icons[agentType] || <RobotOutlined />
  }

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'running':
        return 'processing'
      case 'failed':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatMessageTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const renderWorkflowResults = () => {
    if (workflowResults.length === 0) {
      return (
        <div className="text-center py-8">
          <RobotOutlined className="text-4xl text-gray-300 mb-4" />
          <Text type="secondary">暂无分析结果</Text>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {workflowResults.map((result) => (
          <Card
            key={result.agentType}
            title={
              <div className="flex items-center space-x-2">
                {getAgentIcon(result.agentType)}
                <span>{getAgentName(result.agentType)}</span>
                <Tag color={getStatusColor(result.status)}>
                  {result.status === 'completed'
                    ? '已完成'
                    : result.status === 'running'
                    ? '运行中'
                    : '失败'}
                </Tag>
              </div>
            }
            extra={
              <Button
                size="small"
                onClick={() => executeSpecificAgent(result.agentType)}
                loading={workflowLoading}
                icon={<ReloadOutlined />}
              >
                重新执行
              </Button>
            }
          >
            {result.status === 'completed' && result.result ? (
              <div className="space-y-3">
                <div>
                  <Text strong>分析结果：</Text>
                  <Paragraph className="mt-2 whitespace-pre-wrap">
                    {result.result.analysis}
                  </Paragraph>
                </div>
                {result.result.recommendations && (
                  <div>
                    <Text strong>建议：</Text>
                    <ul className="mt-2 pl-4">
                      {result.result.recommendations.map(
                        (rec: string, index: number) => (
                          <li key={index} className="mb-1">
                            {rec}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
                {result.result.metrics &&
                  Object.keys(result.result.metrics).length > 0 && (
                    <div>
                      <Text strong>关键指标：</Text>
                      <div className="mt-2 grid grid-cols-2 gap-4">
                        {Object.entries(result.result.metrics).map(([key, value]) => (
                          <div key={key} className="bg-gray-50 p-2 rounded">
                            <Text type="secondary">{key}:</Text>
                            <div className="font-semibold">{String(value)}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                <div className="text-xs text-gray-500">
                  执行时间: {new Date(result.timestamp).toLocaleString()}
                  {result.executionTime && ` • 耗时: ${result.executionTime}ms`}
                </div>
              </div>
            ) : result.status === 'failed' ? (
              <Alert
                message="执行失败"
                description={result.error || '未知错误'}
                type="error"
                showIcon
              />
            ) : (
              <div className="text-center py-4">
                <Spin />
                <div className="mt-2">正在执行...</div>
              </div>
            )}
          </Card>
        ))}
      </div>
    )
  }

  const renderWorkflowVisualization = () => {
    if (!workflowExecution) {
      return (
        <div className="text-center py-8">
          <TeamOutlined className="text-4xl text-gray-300 mb-4" />
          <Text type="secondary">执行工作流以查看可视化</Text>
        </div>
      )
    }

    const steps = [
      { title: '财务分析', status: 'finish', icon: <BarChartOutlined /> },
      { title: '投资建议', status: 'finish', icon: <DollarOutlined /> },
      { title: '风险评估', status: 'finish', icon: <SafetyOutlined /> },
      { title: '新闻分析', status: 'finish', icon: <GlobalOutlined /> },
      { title: '结果整合', status: 'finish', icon: <TeamOutlined /> },
    ]

    return (
      <div className="space-y-6">
        <Card title="工作流执行状态">
          <Timeline>
            {steps.map((step, index) => (
              <Timeline.Item
                key={index}
                dot={step.icon}
                color={step.status === 'finish' ? 'green' : 'blue'}
              >
                <div className="flex justify-between items-center">
                  <span>{step.title}</span>
                  <Tag color={step.status === 'finish' ? 'success' : 'processing'}>
                    {step.status === 'finish' ? '已完成' : '进行中'}
                  </Tag>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>

        <Card title="执行统计">
          <Row gutter={16}>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {workflowResults.filter((r) => r.status === 'completed').length}
                </div>
                <div className="text-gray-500">已完成</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {workflowResults.filter((r) => r.status === 'running').length}
                </div>
                <div className="text-gray-500">进行中</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {workflowResults.filter((r) => r.status === 'failed').length}
                </div>
                <div className="text-gray-500">失败</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">
                  {workflowExecution.totalExecutionTime || 0}ms
                </div>
                <div className="text-gray-500">总耗时</div>
              </div>
            </Col>
          </Row>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full">
      <Tabs activeKey={activeTab} onChange={setActiveTab} className="h-full">
        {/* 综合分析标签页 */}
        <TabPane tab="综合分析" key="comprehensive" className="h-full">
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <Title level={4} className="mb-2">
                    AI 工作流分析
                  </Title>
                  <Text type="secondary">
                    使用多个AI代理协同工作，为您提供全面的财务分析和投资建议
                  </Text>
                </div>
                <Space>
                  <Select
                    value={selectedModel}
                    onChange={setSelectedModel}
                    style={{ width: 200 }}
                    placeholder="选择AI模型"
                  >
                    {models.map((model) => (
                      <Option key={model.id} value={model.id}>
                        {model.name}
                      </Option>
                    ))}
                  </Select>
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={executeWorkflow}
                    loading={workflowLoading}
                    size="large"
                  >
                    执行完整分析
                  </Button>
                </Space>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {assets.length === 0 ? (
                <Alert
                  message="提示"
                  description="请先在资产页面添加一些资产，然后再执行AI分析"
                  type="info"
                  showIcon
                  className="mb-4"
                />
              ) : (
                <Alert
                  message={`当前资产: ${assets.length} 项`}
                  description={`总价值: ¥${assets
                    .reduce((sum, asset) => sum + (asset.currentValue || 0), 0)
                    .toLocaleString()}`}
                  type="success"
                  showIcon
                  className="mb-4"
                />
              )}

              {renderWorkflowResults()}
            </div>
          </div>
        </TabPane>

        {/* 单独代理标签页 */}
        <TabPane tab="专项分析" key="agents" className="h-full">
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <Title level={4} className="mb-2">
                专项AI代理
              </Title>
              <Text type="secondary">单独执行特定的AI代理进行专项分析</Text>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card
                    title={
                      <div className="flex items-center space-x-2">
                        <BarChartOutlined />
                        <span>财务分析代理</span>
                      </div>
                    }
                    extra={
                      <Button
                        onClick={() => executeSpecificAgent('financial_analysis')}
                        loading={workflowLoading}
                        icon={<PlayCircleOutlined />}
                      >
                        执行
                      </Button>
                    }
                  >
                    <Text type="secondary">
                      分析您的资产负债情况，计算净资产、负债率等关键财务指标
                    </Text>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card
                    title={
                      <div className="flex items-center space-x-2">
                        <DollarOutlined />
                        <span>投资建议代理</span>
                      </div>
                    }
                    extra={
                      <Button
                        onClick={() => executeSpecificAgent('investment_advisor')}
                        loading={workflowLoading}
                        icon={<PlayCircleOutlined />}
                      >
                        执行
                      </Button>
                    }
                  >
                    <Text type="secondary">
                      基于您的风险偏好和财务状况，提供个性化的投资组合建议
                    </Text>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card
                    title={
                      <div className="flex items-center space-x-2">
                        <SafetyOutlined />
                        <span>风险评估代理</span>
                      </div>
                    }
                    extra={
                      <Button
                        onClick={() => executeSpecificAgent('risk_assessment')}
                        loading={workflowLoading}
                        icon={<PlayCircleOutlined />}
                      >
                        执行
                      </Button>
                    }
                  >
                    <Text type="secondary">
                      评估投资组合的风险水平，分析波动性和相关性
                    </Text>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card
                    title={
                      <div className="flex items-center space-x-2">
                        <GlobalOutlined />
                        <span>新闻分析代理</span>
                      </div>
                    }
                    extra={
                      <Button
                        onClick={() => executeSpecificAgent('news_analysis')}
                        loading={workflowLoading}
                        icon={<PlayCircleOutlined />}
                      >
                        执行
                      </Button>
                    }
                  >
                    <Text type="secondary">
                      分析最新的财经新闻，评估市场情绪对投资组合的影响
                    </Text>
                  </Card>
                </Col>
              </Row>

              <Divider />

              {renderWorkflowResults()}
            </div>
          </div>
        </TabPane>

        {/* 工作流可视化标签页 */}
        <TabPane tab="工作流可视化" key="visualization" className="h-full">
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <Title level={4} className="mb-2">
                工作流可视化
              </Title>
              <Text type="secondary">查看AI代理工作流的执行状态和统计信息</Text>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {renderWorkflowVisualization()}
            </div>
          </div>
        </TabPane>

        {/* AI对话标签页 */}
        <TabPane tab="AI对话" key="chat" className="h-full">
          <div className="h-full flex">
            {/* 左侧对话列表 */}
            <div className="w-80 border-r border-gray-200 flex flex-col">
              <div className="p-4 border-b border-gray-200">
                <div className="flex justify-between items-center mb-4">
                  <Title level={4} className="mb-0">
                    AI 对话
                  </Title>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setNewConversationModal(true)}
                  >
                    新对话
                  </Button>
                </div>

                <Select
                  value={selectedModel}
                  onChange={setSelectedModel}
                  style={{ width: '100%' }}
                  placeholder="选择AI模型"
                >
                  {models.map((model) => (
                    <Option key={model.id} value={model.id}>
                      {model.name}
                    </Option>
                  ))}
                </Select>
              </div>

              <div className="flex-1 overflow-y-auto">
                <List
                  dataSource={conversations}
                  renderItem={(conversation) => (
                    <List.Item
                      className={`cursor-pointer hover:bg-gray-50 ${
                        currentConversation?.id === conversation.id ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => {
                        setCurrentConversation(conversation)
                        loadMessages(conversation.id)
                      }}
                      actions={[
                        <Button
                          type="text"
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteConversation(conversation.id)
                          }}
                        />,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<RobotOutlined />} />}
                        title={conversation.title}
                        description={new Date(
                          conversation.updatedAt
                        ).toLocaleDateString()}
                      />
                    </List.Item>
                  )}
                />
              </div>
            </div>

            {/* 右侧对话区域 */}
            <div className="flex-1 flex flex-col">
              {currentConversation ? (
                <>
                  {/* 对话标题栏 */}
                  <div className="p-4 border-b border-gray-200 bg-white">
                    <div className="flex justify-between items-center">
                      <div>
                        <Title level={4} className="mb-0">
                          {currentConversation.title}
                        </Title>
                        <Text type="secondary">
                          使用模型:{' '}
                          {models.find((m) => m.id === selectedModel)?.name ||
                            selectedModel}
                        </Text>
                      </div>
                    </div>
                  </div>

                  {/* 消息列表 */}
                  <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${
                            message.role === 'user' ? 'justify-end' : 'justify-start'
                          }`}
                        >
                          <div
                            className={`max-w-3xl flex ${
                              message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                            } items-start space-x-3`}
                          >
                            <Avatar
                              icon={
                                message.role === 'user' ? (
                                  <UserOutlined />
                                ) : (
                                  <RobotOutlined />
                                )
                              }
                              className={
                                message.role === 'user' ? 'bg-blue-500' : 'bg-green-500'
                              }
                            />
                            <div
                              className={`p-3 rounded-lg ${
                                message.role === 'user'
                                  ? 'bg-blue-500 text-white'
                                  : 'bg-white border border-gray-200'
                              }`}
                            >
                              <div className="whitespace-pre-wrap">
                                {message.content}
                              </div>
                              <div
                                className={`text-xs mt-2 ${
                                  message.role === 'user'
                                    ? 'text-blue-100'
                                    : 'text-gray-500'
                                }`}
                              >
                                {formatMessageTime(message.createdAt)}
                                {message.tokensUsed && (
                                  <span className="ml-2">
                                    • {message.tokensUsed} tokens
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* 流式消息显示 */}
                      {isStreaming && streamingMessage && (
                        <div className="flex justify-start">
                          <div className="max-w-3xl flex flex-row items-start space-x-3">
                            <Avatar icon={<RobotOutlined />} className="bg-green-500" />
                            <div className="p-3 rounded-lg bg-white border border-gray-200">
                              <div className="whitespace-pre-wrap">
                                {streamingMessage}
                              </div>
                              <Spin size="small" className="mt-2" />
                            </div>
                          </div>
                        </div>
                      )}

                      <div ref={messagesEndRef} />
                    </div>
                  </div>

                  {/* 输入区域 */}
                  <div className="p-4 border-t border-gray-200 bg-white">
                    <div className="flex space-x-2">
                      <TextArea
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        placeholder="输入您的问题..."
                        autoSize={{ minRows: 1, maxRows: 4 }}
                        onPressEnter={(e) => {
                          if (!e.shiftKey) {
                            e.preventDefault()
                            sendMessage()
                          }
                        }}
                      />
                      <Button
                        type="primary"
                        icon={<SendOutlined />}
                        onClick={sendMessage}
                        loading={loading}
                        disabled={!inputMessage.trim()}
                      >
                        发送
                      </Button>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      按 Enter 发送，Shift + Enter 换行
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <RobotOutlined className="text-6xl text-gray-300 mb-4" />
                    <Title level={3} type="secondary">
                      选择或创建一个对话开始聊天
                    </Title>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setNewConversationModal(true)}
                    >
                      创建新对话
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabPane>
      </Tabs>

      {/* 新建对话模态框 */}
      <Modal
        title="创建新对话"
        open={newConversationModal}
        onOk={createNewConversation}
        onCancel={() => {
          setNewConversationModal(false)
          setConversationTitle('')
        }}
        okText="创建"
        cancelText="取消"
      >
        <Input
          placeholder="请输入对话标题"
          value={conversationTitle}
          onChange={(e) => setConversationTitle(e.target.value)}
          onPressEnter={createNewConversation}
        />
      </Modal>
    </div>
  )
}
