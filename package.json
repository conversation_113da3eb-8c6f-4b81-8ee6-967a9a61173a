{"name": "ai-asset-manager", "version": "1.0.0", "description": "AI驱动的资产管理和理财投资建议应用", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"", "dev:frontend": "pnpm --filter frontend dev", "dev:backend": "pnpm --filter backend dev", "build": "pnpm run build:backend && pnpm run build:frontend", "build:frontend": "pnpm --filter frontend build", "build:backend": "pnpm --filter backend build", "start": "pnpm --filter backend start", "start:prod": "pnpm --filter backend start:prod", "test": "pnpm --filter backend test && pnpm --filter frontend test", "lint": "pnpm --filter frontend lint && pnpm --filter backend lint", "lint:fix": "pnpm --filter frontend lint:fix && pnpm --filter backend lint:fix", "db:migrate": "pnpm --filter backend db:migrate", "db:seed": "pnpm --filter backend db:seed", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["ai", "asset-management", "finance", "investment", "typescript", "react", "express"], "author": "AI Asset Manager Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=22.0.0", "pnpm": ">=8.0.0"}}